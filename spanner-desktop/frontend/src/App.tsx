import React, { useState, useEffect } from 'react';
import { Database, Play, Settings, Table, Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import Editor from '@monaco-editor/react';
import {
  ConnectToDatabase,
  GetConnectionStatus,
  ListTables,
  GetTableSchema,
  ExecuteQuery,
  GetConfig
} from '../wailsjs/go/main/App';

interface ConnectionStatus {
  connected: boolean;
  database: string;
  error?: string;
}

interface TableInfo {
  name: string;
  type: string;
  columns?: ColumnInfo[];
}

interface ColumnInfo {
  name: string;
  type: string;
  nullable: boolean;
  default: string;
}

interface QueryResult {
  columns: string[];
  rows: any[][];
  rowCount: number;
  duration: string;
  error?: string;
}

interface SpannerConfig {
  projectId: string;
  instanceId: string;
  databaseId: string;
  emulatorHost: string;
}

function App() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({ connected: false, database: '' });
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [query, setQuery] = useState<string>('SELECT * FROM StreakCompensation LIMIT 10;');
  const [queryResult, setQueryResult] = useState<QueryResult | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [config, setConfig] = useState<SpannerConfig | null>(null);
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    // Load initial configuration and connection status
    loadConfig();
    checkConnectionStatus();
  }, []);

  const loadConfig = async () => {
    try {
      const cfg = await GetConfig();
      setConfig(cfg);
    } catch (error) {
      console.error('Failed to load config:', error);
    }
  };

  const checkConnectionStatus = async () => {
    try {
      const status = await GetConnectionStatus();
      setConnectionStatus(status);
      if (status.connected) {
        loadTables();
      }
    } catch (error) {
      console.error('Failed to check connection status:', error);
    }
  };

  const connectToDatabase = async (databaseId: string) => {
    try {
      const status = await ConnectToDatabase(databaseId);
      setConnectionStatus(status);
      if (status.connected) {
        loadTables();
      }
    } catch (error) {
      console.error('Failed to connect to database:', error);
    }
  };

  const loadTables = async () => {
    try {
      const tableList = await ListTables();
      setTables(tableList);
    } catch (error) {
      console.error('Failed to load tables:', error);
    }
  };

  const loadTableSchema = async (tableName: string) => {
    try {
      const columns = await GetTableSchema(tableName);
      setTables(prev => prev.map(table =>
        table.name === tableName ? { ...table, columns } : table
      ));
    } catch (error) {
      console.error('Failed to load table schema:', error);
    }
  };

  const executeQuery = async () => {
    if (!query.trim()) return;

    setIsExecuting(true);
    try {
      const result = await ExecuteQuery(query);
      setQueryResult(result);
    } catch (error) {
      console.error('Failed to execute query:', error);
      setQueryResult({
        columns: [],
        rows: [],
        rowCount: 0,
        duration: '0ms',
        error: 'Failed to execute query'
      });
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="sidebar">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Database className="w-6 h-6 text-primary-600" />
            <h1 className="text-lg font-semibold text-gray-900">Spanner Desktop</h1>
          </div>
        </div>

        {/* Connection Status */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Connection</span>
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>

          <div className="flex items-center space-x-2">
            {connectionStatus.connected ? (
              <>
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-700">{connectionStatus.database}</span>
              </>
            ) : (
              <>
                <AlertCircle className="w-4 h-4 text-red-500" />
                <span className="text-sm text-red-700">Not connected</span>
              </>
            )}
          </div>

          {!connectionStatus.connected && (
            <div className="mt-2">
              <button
                onClick={() => connectToDatabase('entity-user')}
                className="btn-primary text-xs w-full"
              >
                Connect to entity-user
              </button>
            </div>
          )}
        </div>

        {/* Tables List */}
        {connectionStatus.connected && (
          <div className="flex-1 overflow-y-auto">
            <div className="p-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Tables</h3>
              <div className="space-y-1">
                {tables.map((table) => (
                  <div key={table.name} className="group">
                    <button
                      onClick={() => {
                        setSelectedTable(table.name);
                        if (!table.columns) {
                          loadTableSchema(table.name);
                        }
                        setQuery(`SELECT * FROM ${table.name} LIMIT 10;`);
                      }}
                      className={`w-full text-left px-2 py-1 text-sm rounded hover:bg-gray-100 flex items-center space-x-2 ${
                        selectedTable === table.name ? 'bg-primary-50 text-primary-700' : 'text-gray-700'
                      }`}
                    >
                      <Table className="w-4 h-4" />
                      <span>{table.name}</span>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="main-content">
        {/* Query Editor */}
        <div className="flex-1 flex flex-col">
          <div className="border-b border-gray-200 p-4">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-lg font-semibold text-gray-900">Query Editor</h2>
              <button
                onClick={executeQuery}
                disabled={isExecuting || !connectionStatus.connected}
                className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isExecuting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
                <span>{isExecuting ? 'Executing...' : 'Execute'}</span>
              </button>
            </div>
          </div>

          <div className="flex-1 flex flex-col min-h-0">
            <div className="h-64 border-b border-gray-200">
              <Editor
                height="100%"
                defaultLanguage="sql"
                value={query}
                onChange={(value) => setQuery(value || '')}
                theme="vs-light"
                options={{
                  minimap: { enabled: false },
                  fontSize: 14,
                  lineNumbers: 'on',
                  roundedSelection: false,
                  scrollBeyondLastLine: false,
                  automaticLayout: true,
                  tabSize: 2,
                }}
              />
            </div>

            {/* Results */}
            <div className="flex-1 overflow-hidden">
              {queryResult && (
                <div className="h-full flex flex-col">
                  <div className="p-4 border-b border-gray-200 bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {queryResult.error ? (
                          <span className="text-sm text-red-600 flex items-center space-x-1">
                            <AlertCircle className="w-4 h-4" />
                            <span>Error</span>
                          </span>
                        ) : (
                          <>
                            <span className="text-sm text-gray-600">
                              {queryResult.rowCount} rows
                            </span>
                            <span className="text-sm text-gray-600">
                              {queryResult.duration}
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 overflow-auto">
                    {queryResult.error ? (
                      <div className="p-4">
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                          <p className="text-red-800">{queryResult.error}</p>
                        </div>
                      </div>
                    ) : queryResult.rows.length > 0 ? (
                      <div className="overflow-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50 sticky top-0">
                            <tr>
                              {queryResult.columns.map((column, index) => (
                                <th
                                  key={index}
                                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                  {column}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {queryResult.rows.map((row, rowIndex) => (
                              <tr key={rowIndex} className="hover:bg-gray-50">
                                {row.map((cell, cellIndex) => (
                                  <td
                                    key={cellIndex}
                                    className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono"
                                  >
                                    {cell === null ? (
                                      <span className="text-gray-400 italic">NULL</span>
                                    ) : (
                                      String(cell)
                                    )}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="p-4 text-center text-gray-500">
                        No results
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;