{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@monaco-editor/react": "^4.7.0", "@tailwindcss/forms": "^0.5.10", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "lucide-react": "^0.522.0", "postcss": "^8.5.6", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^4.1.10"}, "devDependencies": {"@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@vitejs/plugin-react": "^2.0.1", "typescript": "^4.6.4", "vite": "^3.0.7"}}