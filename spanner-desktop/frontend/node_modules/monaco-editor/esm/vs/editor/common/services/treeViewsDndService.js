/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { registerSingleton } from '../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../platform/instantiation/common/instantiation.js';
import { TreeViewsDnDService } from './treeViewsDnd.js';
export const ITreeViewsDnDService = createDecorator('treeViewsDndService');
registerSing<PERSON>(ITreeViewsDnDService, TreeViewsDnDService, 1 /* InstantiationType.Delayed */);
