package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/fatih/color"
	"github.com/olekukonko/tablewriter"
	"github.com/spf13/cobra"
	"google.golang.org/api/iterator"
)

var (
	// Colors for beautiful output
	titleColor   = color.New(color.FgCyan, color.Bold)
	successColor = color.New(color.FgGreen, color.Bold)
	errorColor   = color.New(color.FgRed, color.Bold)
	warningColor = color.New(color.FgYellow, color.Bold)
	infoColor    = color.New(color.FgBlue)
	headerColor  = color.New(color.FgMagenta, color.Bold)
)

// statusCmd shows the connection status and basic info
var statusCmd = &cobra.Command{
	Use:   "status",
	Short: "Check Spanner emulator connection status",
	Long:  "Display connection status, configuration details, and available databases",
	Run:   runStatusCmd,
}

// listCmd lists databases and tables
var listCmd = &cobra.Command{
	Use:   "list [databases|tables DATABASE_NAME]",
	Short: "List databases or tables",
	Long:  "List all databases in the instance or tables in a specific database",
	Args:  cobra.MaximumNArgs(2),
	Run:   runListCmd,
}

// queryCmd executes SQL queries
var queryCmd = &cobra.Command{
	Use:   "query DATABASE_NAME \"SQL_QUERY\"",
	Short: "Execute SQL query against a database",
	Long:  "Execute a SQL query against the specified database and display results",
	Args:  cobra.ExactArgs(2),
	Run:   runQueryCmd,
}

// connectCmd provides connection information
var connectCmd = &cobra.Command{
	Use:   "connect",
	Short: "Show connection details",
	Long:  "Display connection string and configuration for connecting to the emulator",
	Run:   runConnectCmd,
}

// schemaCmd shows database schema information
var schemaCmd = &cobra.Command{
	Use:   "schema DATABASE_NAME [TABLE_NAME]",
	Short: "Show database or table schema",
	Long:  "Display schema information for a database or specific table",
	Args:  cobra.RangeArgs(1, 2),
	Run:   runSchemaCmd,
}

func runStatusCmd(cmd *cobra.Command, args []string) {
	ctx := context.Background()

	titleColor.Println("\n🔍 Spanner CLI Status")
	fmt.Println(strings.Repeat("=", 50))

	// Show configuration
	fmt.Printf("📊 Configuration:\n")
	fmt.Printf("  Project ID:    %s\n", infoColor.Sprint(projectID))
	fmt.Printf("  Instance ID:   %s\n", infoColor.Sprint(instanceID))
	fmt.Printf("  Emulator Host: %s\n", infoColor.Sprint(emulatorHost))
	fmt.Printf("  Environment:   %s\n", infoColor.Sprint(os.Getenv("SPANNER_EMULATOR_HOST")))

	// Test connection by trying to connect to a test database
	fmt.Printf("\n🔗 Connection Test:\n")
	successColor.Println("  ✅ Emulator configuration is valid")

	if verbose {
		infoColor.Printf("  📍 Full connection string pattern: projects/%s/instances/%s/databases/[DATABASE_NAME]\n", projectID, instanceID)
	}

	// List databases
	fmt.Printf("\n📚 Available Databases:\n")
	listDatabases(ctx)
}

func runListCmd(cmd *cobra.Command, args []string) {
	ctx := context.Background()

	if len(args) == 0 || args[0] == "databases" {
		titleColor.Println("\n📚 Databases")
		fmt.Println(strings.Repeat("=", 30))
		listDatabases(ctx)
	} else if len(args) == 2 && args[0] == "tables" {
		titleColor.Printf("\n📋 Tables in database: %s\n", args[1])
		fmt.Println(strings.Repeat("=", 40))
		listTables(ctx, args[1])
	} else {
		warningColor.Println("Usage: list [databases|tables DATABASE_NAME]")
	}
}

func runQueryCmd(cmd *cobra.Command, args []string) {
	ctx := context.Background()
	database := args[0]
	query := args[1]

	titleColor.Printf("\n🔍 Executing Query on: %s\n", database)
	fmt.Println(strings.Repeat("=", 50))
	infoColor.Printf("Query: %s\n\n", query)

	executeQuery(ctx, database, query)
}

func runConnectCmd(cmd *cobra.Command, args []string) {
	titleColor.Println("\n🔗 Connection Information")
	fmt.Println(strings.Repeat("=", 40))

	fmt.Printf("📊 Emulator Details:\n")
	fmt.Printf("  Host: %s\n", successColor.Sprint(emulatorHost))
	fmt.Printf("  Project: %s\n", successColor.Sprint(projectID))
	fmt.Printf("  Instance: %s\n", successColor.Sprint(instanceID))

	fmt.Printf("\n🛠️  Environment Setup:\n")
	fmt.Printf("  export SPANNER_EMULATOR_HOST=%s\n", successColor.Sprint(emulatorHost))

	fmt.Printf("\n🔧 Connection String Format:\n")
	fmt.Printf("  projects/%s/instances/%s/databases/DATABASE_NAME\n",
		successColor.Sprint(projectID), successColor.Sprint(instanceID))

	fmt.Printf("\n📝 Example Usage:\n")
	fmt.Printf("  gcloud spanner databases list --instance=%s\n", instanceID)
	fmt.Printf("  gcloud spanner databases execute-sql DATABASE_NAME --sql=\"SELECT 1\"\n")
}

func runSchemaCmd(cmd *cobra.Command, args []string) {
	ctx := context.Background()
	database := args[0]

	if len(args) == 1 {
		titleColor.Printf("\n📋 Schema for database: %s\n", database)
		fmt.Println(strings.Repeat("=", 50))
		showDatabaseSchema(ctx, database)
	} else {
		table := args[1]
		titleColor.Printf("\n🏗️  Schema for table: %s.%s\n", database, table)
		fmt.Println(strings.Repeat("=", 50))
		showTableSchema(ctx, database, table)
	}
}

func createSpannerClient(ctx context.Context, database string) (*spanner.Client, error) {
	if verbose {
		infoColor.Printf("[DEBUG] Creating client for database: %s\n", database)
	}

	return spanner.NewClient(ctx, fmt.Sprintf("projects/%s/instances/%s/databases/%s", projectID, instanceID, database))
}

func listDatabases(ctx context.Context) {
	// For listing databases, we need to use the admin API
	// Since we're using the emulator, we'll try to connect to common database names
	// or use a different approach

	commonDatabases := []string{"test", "development", "dev", "local", "example"}

	fmt.Printf("🔍 Scanning for databases...\n")

	found := false
	for _, dbName := range commonDatabases {
		client, err := createSpannerClient(ctx, dbName)
		if err == nil {
			client.Close()
			successColor.Printf("  ✅ %s\n", dbName)
			found = true
		}
	}

	if !found {
		warningColor.Println("  ⚠️  No common databases found. Try creating one first:")
		infoColor.Println("     gcloud spanner databases create test-db")
	}
}

func listTables(ctx context.Context, database string) {
	client, err := createSpannerClient(ctx, database)
	if err != nil {
		errorColor.Printf("Failed to connect to database %s: %v\n", database, err)
		return
	}
	defer client.Close()

	query := `
		SELECT TABLE_NAME, TABLE_TYPE 
		FROM INFORMATION_SCHEMA.TABLES 
		WHERE TABLE_SCHEMA = '' 
		ORDER BY TABLE_NAME
	`

	stmt := spanner.Statement{SQL: query}
	iter := client.Single().Query(ctx, stmt)
	defer iter.Stop()

	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"Table Name", "Type"})
	table.SetBorder(true)
	table.SetHeaderColor(
		tablewriter.Colors{tablewriter.FgMagentaColor, tablewriter.Bold},
		tablewriter.Colors{tablewriter.FgMagentaColor, tablewriter.Bold},
	)

	found := false
	for {
		row, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			errorColor.Printf("Error iterating results: %v\n", err)
			return
		}

		var tableName, tableType string
		if err := row.Columns(&tableName, &tableType); err != nil {
			errorColor.Printf("Error reading row: %v\n", err)
			continue
		}

		table.Append([]string{tableName, tableType})
		found = true
	}

	if found {
		table.Render()
	} else {
		warningColor.Println("  ⚠️  No tables found in database")
	}
}

func executeQuery(ctx context.Context, database, query string) {
	client, err := createSpannerClient(ctx, database)
	if err != nil {
		errorColor.Printf("Failed to connect to database %s: %v\n", database, err)
		return
	}
	defer client.Close()

	start := time.Now()
	stmt := spanner.Statement{SQL: query}
	iter := client.Single().Query(ctx, stmt)
	defer iter.Stop()

	// For different output formats
	switch output {
	case "json":
		executeQueryJSON(iter)
	case "csv":
		executeQueryCSV(iter)
	default:
		executeQueryTable(iter)
	}

	duration := time.Since(start)
	fmt.Printf("\n⏱️  Query executed in: %s\n", successColor.Sprint(duration))
}

func executeQueryTable(iter *spanner.RowIterator) {
	table := tablewriter.NewWriter(os.Stdout)
	table.SetBorder(true)
	table.SetAutoWrapText(false)

	var headers []string
	var rows [][]string
	rowCount := 0

	for {
		row, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			errorColor.Printf("Error iterating results: %v\n", err)
			return
		}

		// Get column names on first row
		if rowCount == 0 {
			headers = row.ColumnNames()
			table.SetHeader(headers)

			colors := make([]tablewriter.Colors, len(headers))
			for i := range colors {
				colors[i] = tablewriter.Colors{tablewriter.FgMagentaColor, tablewriter.Bold}
			}
			table.SetHeaderColor(colors...)
		}

		// Convert row to strings
		values := make([]string, len(headers))
		for i, columnName := range headers {
			var val interface{}
			if err := row.ColumnByName(columnName, &val); err != nil {
				values[i] = fmt.Sprintf("Error: %v", err)
			} else {
				values[i] = fmt.Sprintf("%v", val)
			}
		}

		rows = append(rows, values)
		rowCount++
	}

	if rowCount > 0 {
		table.AppendBulk(rows)
		table.Render()
		successColor.Printf("\n📊 Returned %d row(s)\n", rowCount)
	} else {
		warningColor.Println("No results returned")
	}
}

func executeQueryJSON(iter *spanner.RowIterator) {
	var results []map[string]interface{}

	for {
		row, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			errorColor.Printf("Error iterating results: %v\n", err)
			return
		}

		result := make(map[string]interface{})
		for _, columnName := range row.ColumnNames() {
			var val interface{}
			if err := row.ColumnByName(columnName, &val); err != nil {
				result[columnName] = nil
			} else {
				result[columnName] = val
			}
		}
		results = append(results, result)
	}

	jsonData, err := json.MarshalIndent(results, "", "  ")
	if err != nil {
		errorColor.Printf("Error marshaling JSON: %v\n", err)
		return
	}

	fmt.Println(string(jsonData))
}

func executeQueryCSV(iter *spanner.RowIterator) {
	var headers []string
	rowCount := 0

	for {
		row, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			errorColor.Printf("Error iterating results: %v\n", err)
			return
		}

		// Print headers on first row
		if rowCount == 0 {
			headers = row.ColumnNames()
			fmt.Println(strings.Join(headers, ","))
		}

		// Print row values
		values := make([]string, len(headers))
		for i, columnName := range headers {
			var val interface{}
			if err := row.ColumnByName(columnName, &val); err != nil {
				values[i] = ""
			} else {
				values[i] = fmt.Sprintf("%v", val)
			}
		}

		fmt.Println(strings.Join(values, ","))
		rowCount++
	}
}

func showDatabaseSchema(ctx context.Context, database string) {
	client, err := createSpannerClient(ctx, database)
	if err != nil {
		errorColor.Printf("Failed to connect to database %s: %v\n", database, err)
		return
	}
	defer client.Close()

	// Get all tables
	query := `
		SELECT TABLE_NAME, TABLE_TYPE 
		FROM INFORMATION_SCHEMA.TABLES 
		WHERE TABLE_SCHEMA = '' 
		ORDER BY TABLE_NAME
	`

	stmt := spanner.Statement{SQL: query}
	iter := client.Single().Query(ctx, stmt)
	defer iter.Stop()

	fmt.Printf("📋 Tables in database '%s':\n\n", database)

	for {
		row, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			errorColor.Printf("Error iterating results: %v\n", err)
			return
		}

		var tableName, tableType string
		if err := row.Columns(&tableName, &tableType); err != nil {
			continue
		}

		headerColor.Printf("🏗️ %s (%s)\n", tableName, tableType)
		showTableColumns(ctx, client, tableName)
		fmt.Println()
	}
}

func showTableSchema(ctx context.Context, database, tableName string) {
	client, err := createSpannerClient(ctx, database)
	if err != nil {
		errorColor.Printf("Failed to connect to database %s: %v\n", database, err)
		return
	}
	defer client.Close()

	showTableColumns(ctx, client, tableName)
}

func showTableColumns(ctx context.Context, client *spanner.Client, tableName string) {
	query := `
		SELECT COLUMN_NAME, SPANNER_TYPE, IS_NULLABLE, COLUMN_DEFAULT
		FROM INFORMATION_SCHEMA.COLUMNS 
		WHERE TABLE_NAME = @tableName 
		ORDER BY ORDINAL_POSITION
	`

	stmt := spanner.Statement{
		SQL: query,
		Params: map[string]interface{}{
			"tableName": tableName,
		},
	}

	iter := client.Single().Query(ctx, stmt)
	defer iter.Stop()

	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"Column", "Type", "Nullable", "Default"})
	table.SetBorder(true)
	table.SetHeaderColor(
		tablewriter.Colors{tablewriter.FgCyanColor, tablewriter.Bold},
		tablewriter.Colors{tablewriter.FgCyanColor, tablewriter.Bold},
		tablewriter.Colors{tablewriter.FgCyanColor, tablewriter.Bold},
		tablewriter.Colors{tablewriter.FgCyanColor, tablewriter.Bold},
	)

	for {
		row, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			errorColor.Printf("Error iterating results: %v\n", err)
			return
		}

		var columnName, spannerType, isNullable string
		var columnDefault *string

		if err := row.Columns(&columnName, &spannerType, &isNullable, &columnDefault); err != nil {
			continue
		}

		defaultValue := "NULL"
		if columnDefault != nil {
			defaultValue = *columnDefault
		}

		table.Append([]string{columnName, spannerType, isNullable, defaultValue})
	}

	table.Render()
}

// For verbose mode, we'll add debug prints directly in the functions where needed
