# Spanner CLI Makefile
# Makes building and installing the CLI tool easy

# Variables
BINARY_NAME=spanner-cli
BUILD_DIR=./bin
INSTALL_DIR=/usr/local/bin
GO_FILES=$(shell find . -name '*.go' -type f)

# Default target
.PHONY: all
all: build

# Build the binary
.PHONY: build
build: $(BUILD_DIR)/$(BINARY_NAME)

$(BUILD_DIR)/$(BINARY_NAME): $(GO_FILES) go.mod go.sum
	@echo "🔨 Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	@go mod tidy
	@go build -o $(BUILD_DIR)/$(BINARY_NAME) .
	@echo "✅ Built $(BINARY_NAME) successfully!"

# Install dependencies
.PHONY: deps
deps:
	@echo "📦 Installing dependencies..."
	@go mod download
	@go mod tidy
	@echo "✅ Dependencies installed!"

# Install the binary to system PATH
.PHONY: install
install: build
	@echo "📥 Installing $(BINARY_NAME) to $(INSTALL_DIR)..."
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME) $(INSTALL_DIR)/
	@sudo chmod +x $(INSTALL_DIR)/$(BINARY_NAME)
	@echo "✅ $(BINARY_NAME) installed successfully!"
	@echo "🚀 You can now run: $(BINARY_NAME)"

# Install locally (without sudo)
.PHONY: install-local
install-local: build
	@echo "📥 Installing $(BINARY_NAME) locally..."
	@mkdir -p $$HOME/.local/bin
	@cp $(BUILD_DIR)/$(BINARY_NAME) $$HOME/.local/bin/
	@chmod +x $$HOME/.local/bin/$(BINARY_NAME)
	@echo "✅ $(BINARY_NAME) installed to $$HOME/.local/bin/"
	@echo "🔧 Make sure $$HOME/.local/bin is in your PATH"
	@echo "   Add this to your shell profile: export PATH=\"$$HOME/.local/bin:$$PATH\""

# Uninstall from system
.PHONY: uninstall
uninstall:
	@echo "🗑️  Uninstalling $(BINARY_NAME)..."
	@sudo rm -f $(INSTALL_DIR)/$(BINARY_NAME)
	@echo "✅ $(BINARY_NAME) uninstalled!"

# Clean build artifacts
.PHONY: clean
clean:
	@echo "🧹 Cleaning build artifacts..."
	@rm -rf $(BUILD_DIR)
	@echo "✅ Clean complete!"

# Run the CLI with status command (for testing)
.PHONY: test-run
test-run: build
	@echo "🧪 Testing $(BINARY_NAME) with status command..."
	@$(BUILD_DIR)/$(BINARY_NAME) status

# Show help
.PHONY: help
help: build
	@echo "🔍 Showing $(BINARY_NAME) help..."
	@$(BUILD_DIR)/$(BINARY_NAME) --help

# Development: run without building
.PHONY: run
run:
	@echo "🏃 Running $(BINARY_NAME) in development mode..."
	@go run . status

# Format Go code
.PHONY: fmt
fmt:
	@echo "🎨 Formatting Go code..."
	@go fmt ./...
	@echo "✅ Code formatted!"

# Run Go linter
.PHONY: lint
lint:
	@echo "🔍 Running Go linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "⚠️  golangci-lint not installed. Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
		go vet ./...; \
	fi
	@echo "✅ Linting complete!"

# Update dependencies
.PHONY: update
update:
	@echo "🔄 Updating dependencies..."
	@go get -u ./...
	@go mod tidy
	@echo "✅ Dependencies updated!"

# Build for multiple platforms
.PHONY: build-all
build-all:
	@echo "🌍 Building for multiple platforms..."
	@mkdir -p $(BUILD_DIR)
	
	@echo "  Building for Linux (amd64)..."
	@GOOS=linux GOARCH=amd64 go build -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 .
	
	@echo "  Building for macOS (amd64)..."
	@GOOS=darwin GOARCH=amd64 go build -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 .
	
	@echo "  Building for macOS (arm64)..."
	@GOOS=darwin GOARCH=arm64 go build -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 .
	
	@echo "  Building for Windows (amd64)..."
	@GOOS=windows GOARCH=amd64 go build -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe .
	
	@echo "✅ Multi-platform build complete!"
	@ls -la $(BUILD_DIR)/

# Create release archive
.PHONY: release
release: build-all
	@echo "📦 Creating release archives..."
	@mkdir -p $(BUILD_DIR)/release
	
	@tar -czf $(BUILD_DIR)/release/$(BINARY_NAME)-linux-amd64.tar.gz -C $(BUILD_DIR) $(BINARY_NAME)-linux-amd64
	@tar -czf $(BUILD_DIR)/release/$(BINARY_NAME)-darwin-amd64.tar.gz -C $(BUILD_DIR) $(BINARY_NAME)-darwin-amd64  
	@tar -czf $(BUILD_DIR)/release/$(BINARY_NAME)-darwin-arm64.tar.gz -C $(BUILD_DIR) $(BINARY_NAME)-darwin-arm64
	@zip -j $(BUILD_DIR)/release/$(BINARY_NAME)-windows-amd64.zip $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe
	
	@echo "✅ Release archives created in $(BUILD_DIR)/release/"
	@ls -la $(BUILD_DIR)/release/

# Quick development workflow
.PHONY: dev
dev: fmt lint build test-run

# Show available targets
.PHONY: targets
targets:
	@echo "🎯 Available Makefile targets:"
	@echo "  build        - Build the binary"
	@echo "  install      - Install to system PATH (requires sudo)"
	@echo "  install-local- Install to ~/.local/bin (no sudo)"
	@echo "  uninstall    - Remove from system PATH"
	@echo "  clean        - Clean build artifacts"
	@echo "  run          - Run in development mode"
	@echo "  test-run     - Build and test with status command"
	@echo "  help         - Show CLI help"
	@echo "  fmt          - Format Go code"
	@echo "  lint         - Run Go linter"
	@echo "  deps         - Install dependencies"
	@echo "  update       - Update dependencies"
	@echo "  build-all    - Build for multiple platforms"
	@echo "  release      - Create release archives"
	@echo "  dev          - Quick development workflow (fmt + lint + build + test)"

# Default target info
.DEFAULT_GOAL := all

# Help target as default when running 'make help'
.PHONY: make-help
make-help:
	@$(MAKE) targets 