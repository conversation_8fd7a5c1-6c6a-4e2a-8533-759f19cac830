#!/bin/bash

# Spanner CLI Installation Script
# This script builds and installs the Spanner CLI tool

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis and styling
CHECKMARK="✅"
CROSS="❌"
ROCKET="🚀"
WRENCH="🔧"
PACKAGE="📦"

echo ""
echo -e "${CYAN}🔍 Spanner CLI Installation${NC}"
echo "=============================================="

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo -e "${CROSS} ${RED}Go is not installed. Please install Go 1.20+ first.${NC}"
    exit 1
fi

echo -e "${CHECKMARK} Go is installed: $(go version)"

# Check Go version
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
if [[ "$(printf '%s\n' "1.20" "$GO_VERSION" | sort -V | head -n1)" = "1.20" ]]; then
    echo -e "${CHECKMARK} Go version is compatible"
else
    echo -e "${YELLOW}⚠️  Warning: Go version might be too old. Recommended: 1.20+${NC}"
fi

# Install dependencies
echo ""
echo -e "${PACKAGE} Installing dependencies..."
if go mod tidy; then
    echo -e "${CHECKMARK} Dependencies installed successfully"
else
    echo -e "${CROSS} ${RED}Failed to install dependencies${NC}"
    exit 1
fi

# Build the binary
echo ""
echo -e "${WRENCH} Building Spanner CLI..."
if go build -o spanner-cli .; then
    echo -e "${CHECKMARK} Build completed successfully"
else
    echo -e "${CROSS} ${RED}Build failed${NC}"
    exit 1
fi

# Test the binary
echo ""
echo -e "${BLUE}🧪 Testing the binary...${NC}"
if ./spanner-cli --help > /dev/null 2>&1; then
    echo -e "${CHECKMARK} Binary works correctly"
else
    echo -e "${CROSS} ${RED}Binary test failed${NC}"
    exit 1
fi

# Ask for installation preference
echo ""
echo -e "${BLUE}Choose installation option:${NC}"
echo "1) Install to /usr/local/bin (system-wide, requires sudo)"
echo "2) Install to ~/.local/bin (user-only, no sudo required)"
echo "3) Keep in current directory only"
echo ""
read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo ""
        echo -e "${WRENCH} Installing to /usr/local/bin..."
        if sudo cp spanner-cli /usr/local/bin/ && sudo chmod +x /usr/local/bin/spanner-cli; then
            echo -e "${CHECKMARK} Installed to /usr/local/bin/"
            INSTALLED_PATH="/usr/local/bin/spanner-cli"
        else
            echo -e "${CROSS} ${RED}Installation failed${NC}"
            exit 1
        fi
        ;;
    2)
        echo ""
        echo -e "${WRENCH} Installing to ~/.local/bin..."
        mkdir -p "$HOME/.local/bin"
        if cp spanner-cli "$HOME/.local/bin/" && chmod +x "$HOME/.local/bin/spanner-cli"; then
            echo -e "${CHECKMARK} Installed to ~/.local/bin/"
            INSTALLED_PATH="$HOME/.local/bin/spanner-cli"
            
            # Check if ~/.local/bin is in PATH
            if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
                echo ""
                echo -e "${YELLOW}⚠️  Warning: ~/.local/bin is not in your PATH${NC}"
                echo -e "${BLUE}Add this to your shell profile (.bashrc, .zshrc, etc.):${NC}"
                echo -e "${GREEN}export PATH=\"\$HOME/.local/bin:\$PATH\"${NC}"
            fi
        else
            echo -e "${CROSS} ${RED}Installation failed${NC}"
            exit 1
        fi
        ;;
    3)
        echo ""
        echo -e "${CHECKMARK} Binary remains in current directory"
        INSTALLED_PATH="./spanner-cli"
        ;;
    *)
        echo -e "${YELLOW}Invalid choice. Binary remains in current directory.${NC}"
        INSTALLED_PATH="./spanner-cli"
        ;;
esac

# Success message
echo ""
echo -e "${GREEN}${CHECKMARK} Installation completed successfully!${NC}"
echo ""
echo -e "${ROCKET} ${CYAN}Quick Start:${NC}"
echo -e "  ${GREEN}$INSTALLED_PATH status${NC}     - Check connection and configuration"
echo -e "  ${GREEN}$INSTALLED_PATH connect${NC}    - Show connection details"
echo -e "  ${GREEN}$INSTALLED_PATH list${NC}       - List available databases"
echo -e "  ${GREEN}$INSTALLED_PATH --help${NC}     - Show all available commands"
echo ""
echo -e "${BLUE}📚 Documentation:${NC} See README.md for detailed usage instructions"
echo ""
echo -e "${CYAN}Happy querying! 🎉${NC}" 