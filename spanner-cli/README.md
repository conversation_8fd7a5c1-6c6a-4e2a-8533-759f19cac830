# 🔍 Spanner CLI - Professional Spanner Emulator Query Tool

A beautiful, developer-friendly terminal application for interacting with your local Google Cloud Spanner emulator. Query databases, explore schemas, and manage your development data with ease.

## ✨ Features

- 🎨 **Beautiful colored output** with emojis and clear formatting
- 📊 **Multiple output formats**: Table, JSON, CSV  
- 🔍 **Schema exploration** with detailed table information
- 🚀 **Fast and intuitive** commands for common operations
- 🛠️ **Developer-friendly** with helpful error messages and examples
- 📋 **Easy database and table listing**
- ⚡ **Quick status checks** and connection testing

## 🚀 Quick Start

### Prerequisites

1. **Spanner Emulator** running on `localhost:9010`
2. **Go 1.24+** installed

### Installation

```bash
# Navigate to the CLI directory
cd tools/spanner-cli

# Install dependencies
go mod tidy

# Build the CLI
go build -o spanner-cli

# Make it executable and add to PATH (optional)
chmod +x spanner-cli
sudo mv spanner-cli /usr/local/bin/
```

### Alternative: Using Make

```bash
# Build and install
make build
make install

# Or just build
make
```

## 📖 Usage

### Basic Commands

```bash
# Show help and available commands
spanner-cli

# Check connection status and configuration
spanner-cli status

# Show connection details
spanner-cli connect
```

### Database Operations

```bash
# List all databases
spanner-cli list databases
spanner-cli list

# List tables in a database
spanner-cli list tables my-database
```

### Querying Data

```bash
# Execute a SQL query (table format)
spanner-cli query my-database "SELECT * FROM users LIMIT 10"

# Execute query with JSON output
spanner-cli query my-database "SELECT * FROM users" --output json

# Execute query with CSV output  
spanner-cli query my-database "SELECT * FROM users" --output csv

# Verbose output for debugging
spanner-cli query my-database "SELECT COUNT(*) FROM users" --verbose
```

### Schema Exploration

```bash
# Show database schema (all tables)
spanner-cli schema my-database

# Show specific table schema
spanner-cli schema my-database users

# Show table details with verbose output
spanner-cli schema my-database users --verbose
```

## 🎨 Output Examples

### Status Command
```
🔍 Spanner CLI Status
==================================================
📊 Configuration:
  Project ID:    backend-core-dev
  Instance ID:   bereal-local-instance
  Emulator Host: localhost:9010
  Environment:   localhost:9010

🔗 Connection Test:
  ✅ Successfully connected to Spanner emulator

📚 Available Databases:
🔍 Scanning for databases...
  ✅ development
  ✅ test
```

### Query Results
```
🔍 Executing Query on: my-database
==================================================
Query: SELECT UserID, FirstName, LastName FROM users LIMIT 5

+--------+-----------+----------+
| UserID | FirstName | LastName |
+--------+-----------+----------+
| 1      | John      | Doe      |
| 2      | Jane      | Smith    |
| 3      | Bob       | Johnson  |
+--------+-----------+----------+

📊 Returned 3 row(s)
⏱️  Query executed in: 45.2ms
```

### Schema Information
```
📋 Schema for database: my-database
==================================================
📋 Tables in database 'my-database':

🏗️ users (BASE TABLE)
+----------+--------------+----------+---------+
| Column   | Type         | Nullable | Default |
+----------+--------------+----------+---------+
| UserID   | INT64        | NO       | NULL    |
| Email    | STRING(255)  | NO       | NULL    |
| Created  | TIMESTAMP    | YES      | NULL    |
+----------+--------------+----------+---------+
```

## ⚙️ Configuration

The CLI uses these default settings:

- **Project ID**: `backend-core-dev`
- **Instance ID**: `bereal-local-instance`  
- **Emulator Host**: `localhost:9010`

To modify these settings, edit the variables in `main.go`:

```go
var (
    projectID    = "your-project-id"
    instanceID   = "your-instance-id"
    emulatorHost = "localhost:9010"
)
```

## 🔧 Global Flags

- `--verbose, -v`: Enable verbose output with debug information
- `--output, -o`: Set output format (`table`, `json`, `csv`)

Examples:
```bash
spanner-cli query mydb "SELECT * FROM users" --output json --verbose
spanner-cli list tables mydb -v
spanner-cli status -o json
```

## 🐛 Troubleshooting

### Emulator Not Running
```
❌ Failed to create client: connection refused
```
**Solution**: Start the Spanner emulator:
```bash
gcloud emulators spanner start
```

### Database Not Found
```
⚠️  No common databases found. Try creating one first:
     gcloud spanner databases create test-db
```
**Solution**: Create a database:
```bash
export SPANNER_EMULATOR_HOST=localhost:9010
gcloud spanner instances create bereal-local-instance --config=emulator-config --description="Local instance"
gcloud spanner databases create test-db --instance=bereal-local-instance
```

### Connection Issues
Make sure the `SPANNER_EMULATOR_HOST` environment variable is set:
```bash
export SPANNER_EMULATOR_HOST=localhost:9010
```

## 🛠️ Development

### Building from Source

```bash
# Clone and build
git clone <repo>
cd tools/spanner-cli
go mod tidy
go build -o spanner-cli

# Run tests (when available)
go test ./...
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 Examples

### Setting up a Test Database

```bash
# 1. Start emulator
gcloud emulators spanner start

# 2. Create instance and database
export SPANNER_EMULATOR_HOST=localhost:9010
gcloud spanner instances create bereal-local-instance --config=emulator-config --description="Local instance"
gcloud spanner databases create test-db --instance=bereal-local-instance

# 3. Create some tables
gcloud spanner databases ddl update test-db --instance=bereal-local-instance \
  --ddl='CREATE TABLE users (
    UserID INT64 NOT NULL,
    Email STRING(255) NOT NULL,
    FirstName STRING(100),
    LastName STRING(100),
    CreatedAt TIMESTAMP
  ) PRIMARY KEY (UserID)'

# 4. Use the CLI
spanner-cli status
spanner-cli list tables test-db  
spanner-cli schema test-db users
```

### Common Queries

```bash
# Count records
spanner-cli query test-db "SELECT COUNT(*) as total_users FROM users"

# Recent records
spanner-cli query test-db "SELECT * FROM users ORDER BY CreatedAt DESC LIMIT 10"

# Aggregate data
spanner-cli query test-db "SELECT COUNT(*) as count, DATE(CreatedAt) as date FROM users GROUP BY DATE(CreatedAt)"

# Export to JSON
spanner-cli query test-db "SELECT * FROM users" --output json > users.json
```

## 🎯 Tips

1. **Use quotes around SQL queries** to avoid shell interpretation issues
2. **Use `--verbose` flag** when debugging connection issues  
3. **Try different output formats** (`--output json|csv|table`) for different use cases
4. **Use `status` command** to verify your setup before running queries
5. **Check `connect` command** for connection string examples

---

**Happy Querying! 🚀** 