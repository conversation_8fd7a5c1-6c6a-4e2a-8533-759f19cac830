package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

var (
	projectID    = "backend-core-dev"
	instanceID   = "bereal-local-instance"
	emulatorHost = "localhost:9010"

	// Global flags
	verbose bool
	output  string
)

func main() {
	// Set emulator host environment variable
	os.Setenv("SPANNER_EMULATOR_HOST", emulatorHost)

	rootCmd := &cobra.Command{
		Use:   "spanner-cli",
		Short: "A professional CLI tool for querying local Spanner emulator",
		Long: `
🔍 Spanner CLI - Professional Spanner Emulator Query Tool

A developer-friendly terminal application for interacting with your local 
Google Cloud Spanner emulator. Query databases, explore schemas, and manage 
your development data with ease.

Configuration:
  Project ID:    ` + projectID + `
  Instance ID:   ` + instanceID + `
  Emulator Host: ` + emulatorHost + `
`,
		Run: func(cmd *cobra.Command, args []string) {
			cmd.Help()
		},
	}

	// Global flags
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "verbose output")
	rootCmd.PersistentFlags().StringVarP(&output, "output", "o", "table", "output format (table, json, csv)")

	// Add commands
	rootCmd.AddCommand(listCmd)
	rootCmd.AddCommand(queryCmd)
	rootCmd.AddCommand(connectCmd)
	rootCmd.AddCommand(schemaCmd)
	rootCmd.AddCommand(statusCmd)

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
